#include "oled.h"
#include "../sequencer/SequencerDefs.h"
#include "../sequencer/ShuffleTemplates.h"

// Constructor implementation
OLEDDisplay::OLEDDisplay() : display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET), initialized(false) {
}

bool OLEDDisplay::begin() {
    // Initialize the display with the I2C address
    if (!display.begin(OLED_I2C_ADDRESS, true)) {
        Serial.println("[ERROR] OLED display initialization failed!");
        return false;
    }
    
    initialized = true;
    
    // Clear the display and show startup message
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SH110X_WHITE);
    display.setCursor(0, 0);
    display.println("PicoMudrasSequencer");
    display.println("OLED Ready");
    display.display();
    
    Serial.println("OLED display initialized successfully");
    return true;
}

void OLEDDisplay::clear() {
    if (!initialized) return;
    
    display.clearDisplay();
    display.display();
}

void OLEDDisplay::update(const UIState& uiState, const Sequencer& seq1, const Sequencer& seq2) {
    if (!initialized) return;

    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SH110X_WHITE);

    const ParamButtonMapping* heldParam = getHeldParameterButton(uiState);

    if (heldParam != nullptr) {
        // Display parameter editing information
        uint8_t voice = uiState.isVoice2Mode ? 2 : 1;
        const Sequencer& currentSeq = uiState.isVoice2Mode ? seq2 : seq1;
        uint8_t currentStep = currentSeq.getCurrentStepForParameter(heldParam->paramId);
        float currentValue = currentSeq.getStepParameterValue(heldParam->paramId, currentStep);
        displayParameterInfo(heldParam->name, currentValue, voice, currentStep);
    } else if (uiState.selectedStepForEdit != -1) {
        // Show current step if step editing mode
        display.setCursor(0, 20);
        display.setTextSize(2);
        display.print("Edit Step: ");
        display.println(uiState.selectedStepForEdit + 1);
    } else {
        // Default screen: Show current scale and shuffle pattern
        display.setTextSize(2);

        // Display Scale Name
        extern const char* SCALE_NAMES[];
        extern int currentScale;
        display.setCursor(0, 10);
        display.print("Scale: ");
        display.println(SCALE_NAMES[currentScale]);

        // Display Shuffle Pattern
        extern const ShuffleTemplate shuffleTemplates[];
        display.setCursor(0, 40);
        display.print("Shuffle: ");
        display.println(shuffleTemplates[uiState.currentShufflePatternIndex].name);
    }

    display.display();
}

void OLEDDisplay::displayParameterInfo(const char* paramName, float currentValue, 
                                      uint8_t voice, uint8_t stepIndex) {
    // Parameter name and voice
    display.setCursor(0, 0);
    display.setTextSize(2);
    display.print(paramName);
    
    // Voice indicator
    display.setTextSize(1);
    display.setCursor(100, 0);
    display.print("V");
    display.println(voice);
    
    // Current step
    display.setCursor(100, 10);
    display.print("S");
    display.println(stepIndex + 1);
    
    // Parameter value (large text)
    display.setTextSize(2);
    display.setCursor(0, 25);
    
    // Format the parameter value based on its type
    ParamId paramId = ParamId::Note; // Default
    
    // Find the ParamId based on the parameter name
    for (size_t i = 0; i < PARAM_BUTTON_MAPPINGS_SIZE; ++i) {
        if (strcmp(PARAM_BUTTON_MAPPINGS[i].name, paramName) == 0) {
            paramId = PARAM_BUTTON_MAPPINGS[i].paramId;
            break;
        }
    }
    
    String formattedValue = formatParameterValue(paramId, currentValue);
    display.println(formattedValue);
    
    // Progress bar for normalized parameters
    if (paramId != ParamId::Note && paramId != ParamId::Octave && 
        paramId != ParamId::Gate && paramId != ParamId::Slide) {
        
        // Draw progress bar
        int barWidth = 100;
        int barHeight = 8;
        int barX = 10;
        int barY = 50;
        
        // Background
        display.drawRect(barX, barY, barWidth, barHeight, SH110X_WHITE);
        
        // Fill based on parameter value (0.0 to 1.0)
        int fillWidth = (int)(currentValue * (barWidth - 2));
        if (fillWidth > 0) {
            display.fillRect(barX + 1, barY + 1, fillWidth, barHeight - 2, SH110X_WHITE);
        }
    }
}

String OLEDDisplay::formatParameterValue(ParamId paramId, float value) {
    switch (paramId) {
        case ParamId::Note:
            return String((int)value);
            
        case ParamId::Velocity:
            return String((int)(value * 100)) + "%";
            
        case ParamId::Filter:
            return String((int)(value * 100)) + "%";
            
        case ParamId::Attack:
            return String(value, 3) + "s";
            
        case ParamId::Decay:
            return String(value, 3) + "s";
            
        case ParamId::Octave:
            if (value < 0.15f) return "-1";
            else if (value > 0.4f) return "+1";
            else return "0";
            
        case ParamId::GateLength:
            return String((int)(value * 100)) + "%";
            
        case ParamId::Gate:
            return value > 0.5f ? "ON" : "OFF";
            
        case ParamId::Slide:
            return value > 0.5f ? "ON" : "OFF";
            
        default:
            return String(value, 2);
    }
}
